package com.lfb.android.footprint.ui.components.settingScreen

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import com.lfb.android.footprint.AboutActivity
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.R
import com.lfb.android.footprint.ui.theme.mainRedColor


@Composable
fun SettingsScreen(
    onBackClick: () -> Unit,
    onImportExportClick: () -> Unit = {},
    onLocationModeClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFF0F1419)) // 深蓝色背景，匹配设计稿
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            Spacer(modifier = Modifier.height(50.dp)) // 状态栏间距

            // 顶部会员信息卡片
            MemberInfoCard()

            Spacer(modifier = Modifier.height(20.dp))

            // 功能按钮区域
            FunctionButtonsSection(
                onImportExportClick = onImportExportClick,
                onLocationModeClick = onLocationModeClick
            )

            Spacer(modifier = Modifier.height(20.dp))

            // 功能卡片列表
            FunctionCardsList()

            Spacer(modifier = Modifier.height(20.dp))

            // 简单功能列表
            SimpleFunctionsList()

            Spacer(modifier = Modifier.height(20.dp)) // 底部按钮空间

            appAboutList()

            Spacer(modifier = Modifier.height(120.dp)) // 底部按钮空间
        }

        // 底部返回按钮
        BackToMapButton(
            onClick = onBackClick,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 50.dp)
        )
    }
}


@Composable
private fun FunctionButtonsSection(
    onImportExportClick: () -> Unit = {},
    onLocationModeClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier.fillMaxWidth()
//            .background(
//                    Color(0xFF1E2A3A)
//                )
                ,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        FunctionButton(
            iconRes = R.drawable.setting_input, // 临时使用现有图标
            text = "导入导出",
            onClick = onImportExportClick
        )
        Spacer(modifier = Modifier.height(6.dp))

        FunctionButton(
            iconRes = R.drawable.setting_icloud,
            text = "数据备份",
            onClick = { /* TODO */ }
        )

        Spacer(modifier = Modifier.height(6.dp))

        FunctionButton(
            iconRes = R.drawable.setting_locmodel,
            text = "定位模式",
            onClick = onLocationModeClick
        )

        Spacer(modifier = Modifier.height(6.dp))

        FunctionButton(
            iconRes = R.drawable.setting_notice,
            text = "通知配置",
            onClick = { /* TODO */ }
        )
    }
}

@Composable
private fun FunctionButton(
    iconRes: Int,
    text: String,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable { onClick() }
            .padding(start = 0.dp, end = 0.dp, top = 2.dp, bottom = 8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(44.dp)
//                .background(
//                    Color(0xFF1E2A3A),
//                    RoundedCornerShape(8.dp),
//                )
            ,
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = iconRes),
                contentDescription = text,
                modifier = Modifier.size(20.dp),
                colorFilter = ColorFilter.tint(mainRedColor)
            )
        }

        Spacer(modifier = Modifier.height(6.dp))

        Text(
            text = text,
            color = Color.White,
            fontSize = 11.sp,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun FunctionCardsList() {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 足迹实验室
        FunctionCard(
            title = "足迹实验室",
            backgroundColor = Color(0xFF1E3A5F),
            onClick = { /* TODO */ }
        )

        // 常去地点
        FunctionCard(
            title = "常去地点",
            subtitle = "添加常去地点可以减少耗电量！",
            backgroundColor = Color(0xFF5A2A2A),
            onClick = { /* TODO */ }
        )
    }
}

@Composable
private fun SimpleFunctionsList() {
    val context = LocalContext.current // 获取上下文

    Column(
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        val functionItems = listOf(
            "使用说明",
            "微信公众号",
            "联系作者",
        )

        functionItems.forEachIndexed { index, item ->
            SimpleFunctionItem(
                title = item,
                onClick = {
                    handleItemClick(context, index) // 传递上下文给函数
                }
            )
        }
    }
}

fun handleItemClick(context: Context, index: Int) {
    // 根据 index 执行不同的操作
    when (index) {
        0 -> {
            /* 处理 "使用说明" 的点击事件 */
            val url = "https://doc.steplife.cn/md/guide.html"
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            context.startActivity(intent)
        }
        1 -> {
            // 处理 "微信公众号" 的点击事件

        }
        2 -> { /* 处理 "联系作者" 的点击事件 */
            // 打开小红书
            // 处理 "联系作者" 的点击事件，尝试打开小红书

//            val intent = Intent(
//                Intent.ACTION_VIEW,
//                Uri.parse("https://www.xiaohongshu.com/user/profile/5c619d46000000001a02eedc")
//            )
            openXiaohongshuProfile(context, "https://www.xiaohongshu.com/user/profile/5c619d46000000001a02eedc")
//            val packageManager = context.packageManager
//            val intent: Intent? = packageManager.getLaunchIntentForPackage("com.xingin.xhs")
//            intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

//
//            if (intent != null) {
//                context.startActivity(intent)
//            } else {
//                val webUrl = "https://www.xiaohongshu.com/user/profile/5c619d46000000001a02eedc"  // 小红书网页链接
//                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(webUrl))
//                context.startActivity(intent)
//            }
        }
        // 其他情况...
    }
}


/**
 * 打开小红书作者主页
 * @param context 上下文
 * @param profileUrl 小红书主页链接，例如：https://www.xiaohongshu.com/user/你的主页ID
 */
fun openXiaohongshuProfile(context: Context, profileUrl: String) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(profileUrl))
    // 优先用小红书APP打开
    intent.setPackage("com.xingin.xhs")
    try {
        context.startActivity(intent)
    } catch (e: Exception) {
        // 没有安装小红书APP，使用系统浏览器打开
        intent.setPackage(null)
        try {
            context.startActivity(intent)
        } catch (e: Exception) {
//            Toast.makeText(context, "无法打开链接", Toast.LENGTH_SHORT).show()
        }
    }
}

@Composable
private fun appAboutList() {
    val context = LocalContext.current // 获取上下文

    Column(
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        val functionItems = listOf(
            "分享「足迹」",
            "关于「足迹」",
        )

        functionItems.forEachIndexed { index, item ->
            SimpleFunctionItem(
                title = item,
                onClick = {
                    when (index) {
                        0 -> {
                            // 分享功能
                            // TODO: 实现分享功能
                        }
                        1 -> {
                            // 跳转到关于页面
                            val intent = Intent(context, AboutActivity::class.java)
                            context.startActivity(intent)
                        }
                    }
                }
            )
        }
    }
}


fun isAppAvailable(context: Context, url: String): Boolean {
    val packageManager = context.packageManager
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
    val activities = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
    return activities.isNotEmpty()
}

@Composable
private fun FunctionCard(
    title: String,
    subtitle: String? = null,
    backgroundColor: Color,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(if (subtitle != null) 70.dp else 50.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.CenterStart
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = title,
                    color = Color.White,
                    fontSize = 15.sp,
                    fontWeight = FontWeight.Medium
                )

                subtitle?.let {
                    Spacer(modifier = Modifier.height(3.dp))
                    Text(
                        text = it,
                        color = Color.White.copy(alpha = 0.6f),
                        fontSize = 11.sp
                    )
                }
            }
        }
    }
}

@Composable
private fun SimpleFunctionItem(
    title: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            color = Color.White,
            fontSize = 15.sp,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun BackToMapButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier
            .width(160.dp)
            .height(44.dp),
        shape = RoundedCornerShape(22.dp),
        border = BorderStroke(1.dp, mainRedColor),
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = Color.Transparent,
            contentColor = mainRedColor
        )
    ) {
        Text(
            text = "返回地图",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}
