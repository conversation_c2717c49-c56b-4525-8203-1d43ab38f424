package com.lfb.android.footprint.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.os.PowerManager
import android.app.NotificationManager
import android.app.NotificationChannel
import android.os.Build
import androidx.core.app.NotificationCompat
import com.lfb.android.footprint.MainMapActivity
import com.lfb.android.footprint.R
import com.lfb.android.footprint.location.LocationManager
import com.lfb.android.footprint.geofence.GeofenceManager


class LocationService : Service() {
    private val locationManager by lazy { LocationManager.getInstance(this) }
    private val geofenceManager by lazy { GeofenceManager.getInstance(this) }
    private val NOTIFICATION_ID = 1
    private val CHANNEL_ID = "location_service_channel"

    // WakeLock相关
    private var wakeLock: PowerManager.WakeLock? = null

    override fun onCreate() {
        super.onCreate()
        LocalLogManager.getInstance(this).logSync("LocationService: onCreate() 开始")
        createNotificationChannel()
        acquireWakeLock()
        LocalLogManager.getInstance(this).logSync("LocationService: onCreate() 完成")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LocalLogManager.getInstance(this).logSync("LocationService: onStartCommand() 开始, action=${intent?.action}")

        // 创建通知
        val notification = createNotification()

        // 启动前台服务
        startForeground(NOTIFICATION_ID, notification)
        LocalLogManager.getInstance(this).logSync("LocationService: 前台服务已启动")

        // 开始位置更新
        locationManager.startLocationUpdates()
        LocalLogManager.getInstance(this).logSync("LocationService: 位置更新已开始")

        // 处理地理围栏触发的启动
        if (intent?.action == "GEOFENCE_TRIGGERED_START") {
            LocalLogManager.getInstance(this).logSync("LocationService: 由地理围栏触发启动")
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Location Service"
            val descriptionText = "用于后台记录位置信息"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }
            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val notificationIntent = Intent(this, MainMapActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
//            .setContentTitle("足迹记录中")
            .setContentText("正在后台记录您的轨迹")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .build()
    }

    override fun onDestroy() {
        LocalLogManager.getInstance(this).logSync("LocationService: onDestroy() 开始")
        super.onDestroy()
        locationManager.stopLocationUpdates()
        LocalLogManager.getInstance(this).logSync("LocationService: 位置更新已停止")
        releaseWakeLock()
        restartService()
        LocalLogManager.getInstance(this).logSync("LocationService: onDestroy() 完成，服务重启已触发")
    }

    private fun acquireWakeLock() {
        try {
            LocalLogManager.getInstance(this).logSync("LocationService: 开始获取WakeLock")
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "FootPrint:LocationTrackingWakeLock"
            )
            wakeLock?.setReferenceCounted(false)
            // 设置较长的超时时间，或者不设置超时（但要确保正确释放）
            wakeLock?.acquire()
            LocalLogManager.getInstance(this).logSync("LocationService: WakeLock已成功获取，isHeld=${wakeLock?.isHeld}")
        } catch (e: Exception) {
            LocalLogManager.getInstance(this).logSync("LocationService: 获取WakeLock失败: ${e.message}")
        }
    }

    private fun releaseWakeLock() {
        try {
            LocalLogManager.getInstance(this).logSync("LocationService: 开始释放WakeLock")
            wakeLock?.let { wl ->
                if (wl.isHeld) {
                    wl.release()
                    LocalLogManager.getInstance(this).logSync("LocationService: WakeLock已成功释放")
                } else {
                    LocalLogManager.getInstance(this).logSync("LocationService: WakeLock未持有，无需释放")
                }
            } ?: run {
                LocalLogManager.getInstance(this).logSync("LocationService: WakeLock为null，无需释放")
            }
            wakeLock = null
        } catch (e: Exception) {
            LocalLogManager.getInstance(this).logSync("LocationService: 释放WakeLock失败: ${e.message}")
        }
    }

    private fun restartService() {
        try {
            LocalLogManager.getInstance(this).logSync("LocationService: 开始重启服务")
            val intent = Intent(this, LocationService::class.java)
            intent.action = "START_TRACKING"
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
                LocalLogManager.getInstance(this).logSync("LocationService: 前台服务重启命令已发送")
            } else {
                startService(intent)
                LocalLogManager.getInstance(this).logSync("LocationService: 普通服务重启命令已发送")
            }
        } catch (e: Exception) {
            LocalLogManager.getInstance(this).logSync("LocationService: 重启服务失败: ${e.message}")
        }
    }
}