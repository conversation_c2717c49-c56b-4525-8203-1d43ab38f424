package com.lfb.android.footprint.geofence

import android.content.Context
import android.content.Intent
import android.location.Location
import android.os.Build
import com.lfb.android.footprint.Manager.LocalLogManager
import com.lfb.android.footprint.service.LocationService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs

/**
 * 原生地理围栏管理器
 * 不依赖Google Play Services，使用纯Android API实现
 * 通过定期检查位置变化来模拟地理围栏功能
 */
class NativeGeofenceManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: NativeGeofenceManager? = null
        
        // 地理围栏半径（米）
        private const val GEOFENCE_RADIUS = 100f
        
        // 检查间隔（毫秒）
        private const val CHECK_INTERVAL = 30000L // 30秒
        
        // 地理围栏ID前缀
        private const val GEOFENCE_ID_PREFIX = "native_geofence_"

        fun getInstance(context: Context): NativeGeofenceManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NativeGeofenceManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val localLogManager by lazy {
        LocalLogManager.getInstance(context)
    }

    // 当前地理围栏信息
    private var currentGeofenceId: String? = null
    private var currentGeofenceLocation: Location? = null
    private var currentGeofenceRadius: Float = GEOFENCE_RADIUS
    
    // 上次已知位置
    private var lastKnownLocation: Location? = null
    
    // 是否在围栏内
    private var isInsideGeofence: Boolean = false
    
    // 监控协程
    private var monitoringJob: Job? = null
    private val monitoringScope = CoroutineScope(Dispatchers.IO)

    /**
     * 创建地理围栏
     * @param location 围栏中心位置
     * @param radius 围栏半径（米）
     */
    fun createGeofence(location: Location, radius: Float = GEOFENCE_RADIUS) {
        try {
            // 停止之前的监控
            stopMonitoring()

            // 生成新的地理围栏ID
            val geofenceId = "${GEOFENCE_ID_PREFIX}${System.currentTimeMillis()}"
            
            // 设置围栏信息
            currentGeofenceId = geofenceId
            currentGeofenceLocation = location.clone() as Location
            currentGeofenceRadius = radius
            lastKnownLocation = location.clone() as Location
            isInsideGeofence = true // 创建时假设在围栏内
            
            localLogManager.logSync(
                "NativeGeofenceManager: 地理围栏创建成功 - " +
                "ID: $geofenceId, " +
                "位置: (${location.latitude}, ${location.longitude}), " +
                "半径: ${radius}m"
            )
            
            // 开始监控
            startMonitoring()

        } catch (e: Exception) {
            localLogManager.logSync(
                "NativeGeofenceManager: 创建地理围栏时发生异常 - ${e.message}"
            )
        }
    }

    /**
     * 更新当前位置
     * 由LocationManager调用，用于检查是否离开围栏
     */
    fun updateLocation(location: Location) {
        try {
            if (currentGeofenceLocation == null) {
                localLogManager.logSync("NativeGeofenceManager: 没有活跃的地理围栏，跳过位置更新")
                return
            }

            lastKnownLocation = location.clone() as Location
            
            // 检查是否离开围栏
            checkGeofenceTransition(location)

        } catch (e: Exception) {
            localLogManager.logSync(
                "NativeGeofenceManager: 更新位置时发生异常 - ${e.message}"
            )
        }
    }

    /**
     * 检查地理围栏转换
     */
    private fun checkGeofenceTransition(location: Location) {
        val geofenceLocation = currentGeofenceLocation ?: return
        
        val distance = location.distanceTo(geofenceLocation)
        val wasInside = isInsideGeofence
        val nowInside = distance <= currentGeofenceRadius
        
        localLogManager.logSync(
            "NativeGeofenceManager: 位置检查 - " +
            "距离围栏中心: ${distance.toInt()}m, " +
            "围栏半径: ${currentGeofenceRadius.toInt()}m, " +
            "之前状态: ${if (wasInside) "内部" else "外部"}, " +
            "当前状态: ${if (nowInside) "内部" else "外部"}"
        )

        if (wasInside && !nowInside) {
            // 离开围栏
            handleGeofenceExit(location)
        } else if (!wasInside && nowInside) {
            // 进入围栏
            handleGeofenceEnter(location)
        }
        
        isInsideGeofence = nowInside
    }

    /**
     * 处理离开围栏事件
     */
    private fun handleGeofenceExit(location: Location) {
        try {
            localLogManager.logSync("NativeGeofenceManager: 离开地理围栏，开始后台唤醒流程")

            // 启动定位服务
            ensureLocationServiceRunning()

            // 在新位置创建新的地理围栏
            createGeofence(location)

            localLogManager.logSync("NativeGeofenceManager: 后台唤醒流程完成")

        } catch (e: Exception) {
            localLogManager.logSync(
                "NativeGeofenceManager: 处理离开围栏事件时发生异常 - ${e.message}"
            )
        }
    }

    /**
     * 处理进入围栏事件
     */
    private fun handleGeofenceEnter(location: Location) {
        localLogManager.logSync("NativeGeofenceManager: 进入地理围栏")
    }

    /**
     * 确保定位服务正在运行
     */
    private fun ensureLocationServiceRunning() {
        try {
            localLogManager.logSync("NativeGeofenceManager: 检查并启动定位服务")
            
            val intent = Intent(context, LocationService::class.java).apply {
                action = "GEOFENCE_TRIGGERED_START"
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
                localLogManager.logSync("NativeGeofenceManager: 前台定位服务启动命令已发送")
            } else {
                context.startService(intent)
                localLogManager.logSync("NativeGeofenceManager: 定位服务启动命令已发送")
            }
            
        } catch (e: Exception) {
            localLogManager.logSync(
                "NativeGeofenceManager: 启动定位服务失败 - ${e.message}"
            )
        }
    }

    /**
     * 开始监控
     * 定期检查位置变化（作为备用机制）
     */
    private fun startMonitoring() {
        monitoringJob = monitoringScope.launch {
            try {
                localLogManager.logSync("NativeGeofenceManager: 开始定期监控")
                
                while (true) {
                    delay(CHECK_INTERVAL)
                    
                    lastKnownLocation?.let { location ->
                        checkGeofenceTransition(location)
                    }
                }
                
            } catch (e: Exception) {
                localLogManager.logSync(
                    "NativeGeofenceManager: 监控过程中发生异常 - ${e.message}"
                )
            }
        }
    }

    /**
     * 停止监控
     */
    private fun stopMonitoring() {
        monitoringJob?.cancel()
        monitoringJob = null
        localLogManager.logSync("NativeGeofenceManager: 停止监控")
    }

    /**
     * 移除当前地理围栏
     */
    fun removeCurrentGeofence() {
        try {
            currentGeofenceId?.let { geofenceId ->
                localLogManager.logSync("NativeGeofenceManager: 移除地理围栏 - ID: $geofenceId")
                
                stopMonitoring()
                currentGeofenceId = null
                currentGeofenceLocation = null
                lastKnownLocation = null
                isInsideGeofence = false
            }
        } catch (e: Exception) {
            localLogManager.logSync(
                "NativeGeofenceManager: 移除地理围栏时发生异常 - ${e.message}"
            )
        }
    }

    /**
     * 移除所有地理围栏
     */
    fun removeAllGeofences() {
        removeCurrentGeofence()
    }

    /**
     * 检查是否需要更新地理围栏
     */
    fun checkAndUpdateGeofence(newLocation: Location, threshold: Float = GEOFENCE_RADIUS / 2) {
        currentGeofenceLocation?.let { currentLocation ->
            val distance = currentLocation.distanceTo(newLocation)
            if (distance > threshold) {
                localLogManager.logSync(
                    "NativeGeofenceManager: 位置变化超过阈值(${threshold}m)，当前距离: ${distance}m，更新地理围栏"
                )
                createGeofence(newLocation)
            }
        } ?: run {
            localLogManager.logSync("NativeGeofenceManager: 没有活跃的地理围栏，创建新围栏")
            createGeofence(newLocation)
        }
    }

    /**
     * 获取当前地理围栏信息
     */
    fun getCurrentGeofenceInfo(): Pair<String?, Location?> {
        return Pair(currentGeofenceId, currentGeofenceLocation)
    }

    /**
     * 检查地理围栏是否活跃
     */
    fun isGeofenceActive(): Boolean {
        return currentGeofenceId != null
    }

    /**
     * 获取地理围栏状态信息
     */
    fun getGeofenceStatus(): String {
        return buildString {
            append("围栏活跃: ${isGeofenceActive()}\n")
            append("围栏ID: ${currentGeofenceId ?: "无"}\n")
            append("围栏位置: ${currentGeofenceLocation?.let { "(${it.latitude}, ${it.longitude})" } ?: "无"}\n")
            append("围栏半径: ${currentGeofenceRadius.toInt()}m\n")
            append("当前状态: ${if (isInsideGeofence) "围栏内" else "围栏外"}\n")
            append("最后位置: ${lastKnownLocation?.let { "(${it.latitude}, ${it.longitude})" } ?: "无"}")
        }
    }
}
