# 地理围栏系统使用说明

## 概述

本地理围栏系统为FootPrint应用提供了完善的后台定位唤醒功能。当用户离开当前位置100米范围时，系统会自动触发，确保定位服务持续运行。

## 主要功能

1. **自动创建地理围栏**：首次启动时在当前位置创建100米半径的地理围栏
2. **动态更新围栏**：当用户移动超过50米时，自动在新位置创建新的地理围栏
3. **后台唤醒**：离开围栏时自动启动LocationService，确保持续定位
4. **智能管理**：自动移除旧围栏，避免资源浪费

## 核心组件

### 1. GeofenceManager
- 负责地理围栏的创建、管理和监听
- 提供围栏状态查询和更新功能
- 自动处理权限检查

### 2. GeofenceBroadcastReceiver
- 处理地理围栏触发事件
- 在离开围栏时启动定位服务
- 在新位置创建新的地理围栏

### 3. GeofenceServiceManager
- 管理LocationService的运行状态
- 提供服务启动、停止和状态检查功能
- 确保服务在需要时正常运行

### 4. GeofenceInitializer
- 提供简单的初始化API
- 支持系统重置和状态查询
- 便于集成到应用启动流程

## 使用方法

### 基本初始化

```kotlin
// 在应用启动时初始化地理围栏系统
val success = GeofenceInitializer.initialize(context)
if (success) {
    Log.d("Geofence", "地理围栏系统初始化成功")
} else {
    Log.e("Geofence", "地理围栏系统初始化失败")
}
```

### 检查系统状态

```kotlin
// 检查地理围栏系统是否正常运行
val isRunning = GeofenceInitializer.isRunning(context)
Log.d("Geofence", "系统运行状态: $isRunning")

// 获取详细状态信息
val statusInfo = GeofenceInitializer.getStatus(context)
Log.d("Geofence", "状态信息: $statusInfo")
```

### 重置系统

```kotlin
// 重置地理围栏系统（清除所有围栏和停止服务）
val success = GeofenceInitializer.reset(context)
Log.d("Geofence", "系统重置结果: $success")
```

### 手动管理地理围栏

```kotlin
// 获取地理围栏管理器
val geofenceManager = GeofenceManager.getInstance(context)

// 在指定位置创建地理围栏
val location = Location("").apply {
    latitude = 39.9042
    longitude = 116.4074
}
geofenceManager.createGeofence(location)

// 检查并更新地理围栏
geofenceManager.checkAndUpdateGeofence(newLocation)

// 移除所有地理围栏
geofenceManager.removeAllGeofences()
```

## 工作流程

1. **应用启动**：调用`GeofenceInitializer.initialize()`初始化系统
2. **首次定位**：LocationService获取到位置后，创建初始地理围栏
3. **位置更新**：用户移动时，系统检查是否需要更新围栏位置
4. **离开围栏**：触发GeofenceBroadcastReceiver，启动定位服务并创建新围栏
5. **持续监控**：循环执行步骤3-4，确保持续定位

## 权限要求

系统已自动配置以下权限：
- `ACCESS_FINE_LOCATION`：精确位置权限
- `ACCESS_COARSE_LOCATION`：粗略位置权限
- `ACCESS_BACKGROUND_LOCATION`：后台位置权限（Android 10+）

## 配置参数

可在GeofenceManager中调整以下参数：
- `GEOFENCE_RADIUS`：地理围栏半径（默认100米）
- `GEOFENCE_LOITERING_DELAY`：触发延迟（默认1秒）
- 更新阈值：移动距离超过围栏半径的一半时更新（默认50米）

## 日志记录

所有地理围栏相关操作都会通过LocalLogManager记录详细日志，便于调试和监控：
- 围栏创建和移除
- 触发事件和处理结果
- 服务启动和状态变化
- 异常和错误信息

## 注意事项

1. **电池优化**：某些设备可能需要将应用加入电池优化白名单
2. **权限申请**：确保用户已授予所有必要的位置权限
3. **网络依赖**：地理围栏功能依赖Google Play Services
4. **测试建议**：在真实设备上测试，模拟器可能无法正确模拟地理围栏
