package com.lfb.android.footprint.location

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.os.Build
import android.os.Looper
import android.util.Log
import android.annotation.SuppressLint
import android.icu.text.SimpleDateFormat
import com.google.android.gms.location.*
import com.lfb.android.footprint.Manager.AppNotificationManager
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.location.GpsOffsetFilter
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.Locale
import kotlin.math.abs
import kotlin.math.sqrt

class LocationManager private constructor(private val context: Context) {

    private val TAG = "LocationManager"

    private val _locationFlow = MutableSharedFlow<Location>()
    val locationFlow = _locationFlow.asSharedFlow()

    private val _gpsSignalFlow = MutableStateFlow(0)
    val gpsSignalFlow = _gpsSignalFlow.asStateFlow()

    private val systemLocationManager: android.location.LocationManager by lazy {
        context.getSystemService(Context.LOCATION_SERVICE) as android.location.LocationManager
    }
    private val locationDataRecorder by lazy { LocationDataRecorder.getInstance(this.context) }
    private val gpsOffsetFilter by lazy { GpsOffsetFilter(context) }

    var lastLocation: Location? = null

    init {
        lastLocation = locationDataRecorder.lastLocation
    }

    private val locationListener = android.location.LocationListener { location ->
//        Log.e(TAG, "locationupdate: ${location}")

        var theLocationAllowed = false
        var locationDistance = 0f
        val previousLocation = this.lastLocation // 将 this.lastLocation 赋给一个局部变量，更清晰

        if (previousLocation != null) {
            // 判断是否是同一天
            val sameDay = SimpleDateFormat("yyyyMMdd", Locale.getDefault()).let { sdf ->
                sdf.format(location.time) == sdf.format(previousLocation.time)
            }
            if (sameDay == false) {
                // 新的一天
                theLocationAllowed = true
            } else {
                // 同一天
                val subTime = abs(location.time - previousLocation.time)
                if (subTime > 1000) {
                    var distanceAllow = 100.0;
                    if (location.speed > 0) {
                        distanceAllow = 20+sqrt(location.speed*5)*6.5;
                    }

                    val distance = location.distanceTo(previousLocation)
                    locationDistance = distance
                    if (distance > distanceAllow) {
                        theLocationAllowed = true
                    } else {
                        LocalLogManager.getInstance(this.context).logSync("distance(${distance}) distanceAllow(${distanceAllow}) subTime(${subTime}) speed(${location.speed}) lc: ${location}")
                    }
                }
            }
        } else {
            theLocationAllowed = true
        }

        // 应用GPS偏移过滤器
        if (theLocationAllowed) {
            // 首先记录原始定位数据（未经过滤的）
            locationDataRecorder.recordRawLocation(location)

            theLocationAllowed = gpsOffsetFilter.shouldAcceptLocation(location)
            if (!theLocationAllowed) {
                LocalLogManager.getInstance(this.context).logSync("GPS偏移过滤器拒绝: lat=${location.latitude}, lng=${location.longitude}, accuracy=${location.accuracy}")
            }
        }

        this.lastLocation = location

        if (theLocationAllowed) {
            CoroutineScope(Dispatchers.IO).launch {
                _locationFlow.emit(location)
                locationDataRecorder.recordLocation(location)
            }
        }

        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val title = "位置更新:${sdf.format(location.time)}"
        val text = String.format(
            "%s %.1f 当前位置: %.3f, %.3f",
            theLocationAllowed,
            locationDistance,
            location.latitude,
            location.longitude
        )

        AppNotificationManager.showLocationUpdateNotification(context, title, text)
    }

    @SuppressLint("MissingPermission")
    fun startLocationUpdates() {
        if (!hasRequiredPermissions()) return

        var minTime = 1200L
        var minDistance = 100f

        if (AppPrefs.sharedInstance.runningModel == 1)  {
            // 耗电模式
            minTime = 1000L
            minDistance = 20f
        } else if (AppPrefs.sharedInstance.runningModel == 2)  {
            // 省电模式
            minTime = 2000L
            minDistance = 200f
        }

        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
            systemLocationManager.requestLocationUpdates(
                android.location.LocationManager.GPS_PROVIDER,
                minTime,
                minDistance,
                locationListener,
                Looper.getMainLooper()
            )
        }

        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
            systemLocationManager.requestLocationUpdates(
                android.location.LocationManager.NETWORK_PROVIDER,
                minTime,
                minDistance,
                locationListener,
                Looper.getMainLooper()
            )
        }
    }

    private fun hasRequiredPermissions(): Boolean {
        return context.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
               context.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
               (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q ||
                context.checkSelfPermission(Manifest.permission.ACCESS_BACKGROUND_LOCATION) == PackageManager.PERMISSION_GRANTED)
    }

    fun stopLocationUpdates() {
        systemLocationManager.removeUpdates(locationListener)
    }

    companion object {
        @Volatile
        private var instance: LocationManager? = null

        fun getInstance(context: Context): LocationManager {
            return instance ?: synchronized(this) {
                instance ?: LocationManager(context.applicationContext).also { instance = it }
            }
        }
    }
}