package com.lfb.android.footprint.geofence

import android.Manifest
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.os.Build
import androidx.core.content.ContextCompat
import com.google.android.gms.location.*

/**
 * 地理围栏管理器
 * 负责创建、管理和监听地理围栏
 * 主要用于后台唤醒定位服务
 */
class GeofenceManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: GeofenceManager? = null
        
        // 地理围栏半径（米）
        private const val GEOFENCE_RADIUS = 200f
        
        // 地理围栏ID前缀
        private const val GEOFENCE_ID_PREFIX = "footprint_geofence_"
        
        // 地理围栏过期时间（毫秒）- 设置为永不过期
        private const val GEOFENCE_EXPIRATION = Geofence.NEVER_EXPIRE
        
        // 地理围栏触发延迟（毫秒）
        private const val GEOFENCE_LOITERING_DELAY = 1000

        fun getInstance(context: Context): GeofenceManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: GeofenceManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val geofencingClient: GeofencingClient by lazy {
        LocationServices.getGeofencingClient(context)
    }
    
    private val localLogManager by lazy {
        LocalLogManager.getInstance(context)
    }

    // 当前活跃的地理围栏ID
    private var currentGeofenceId: String? = null
    
    // 当前地理围栏的中心位置
    private var currentGeofenceLocation: Location? = null

    /**
     * 检查是否有地理围栏权限
     */
    private fun hasGeofencePermissions(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED &&
        ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED &&
        (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q ||
         ContextCompat.checkSelfPermission(
             context,
             Manifest.permission.ACCESS_BACKGROUND_LOCATION
         ) == PackageManager.PERMISSION_GRANTED)
    }

    /**
     * 创建地理围栏PendingIntent
     */
    private fun getGeofencePendingIntent(): PendingIntent {
        val intent = Intent(context, GeofenceBroadcastReceiver::class.java)
        return PendingIntent.getBroadcast(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
        )
    }

    /**
     * 创建地理围栏
     * @param location 围栏中心位置
     * @param radius 围栏半径（米）
     */
    fun createGeofence(location: Location, radius: Float = GEOFENCE_RADIUS) {
        if (!hasGeofencePermissions()) {
            localLogManager.logSync("GeofenceManager: 缺少地理围栏权限，无法创建围栏")
            return
        }

        try {
            // 先移除现有的地理围栏
            removeCurrentGeofence()

            // 生成新的地理围栏ID
            val geofenceId = "${GEOFENCE_ID_PREFIX}${System.currentTimeMillis()}"
            
            // 创建地理围栏对象
            val geofence = Geofence.Builder()
                .setRequestId(geofenceId)
                .setCircularRegion(
                    location.latitude,
                    location.longitude,
                    radius
                )
                .setExpirationDuration(GEOFENCE_EXPIRATION)
                .setTransitionTypes(
                    Geofence.GEOFENCE_TRANSITION_EXIT or 
                    Geofence.GEOFENCE_TRANSITION_ENTER
                )
                .setLoiteringDelay(GEOFENCE_LOITERING_DELAY)
                .build()

            // 创建地理围栏请求
            val geofencingRequest = GeofencingRequest.Builder()
                .setInitialTrigger(GeofencingRequest.INITIAL_TRIGGER_EXIT)
                .addGeofence(geofence)
                .build()

            // 添加地理围栏
            geofencingClient.addGeofences(geofencingRequest, getGeofencePendingIntent())
                .addOnSuccessListener {
                    currentGeofenceId = geofenceId
                    currentGeofenceLocation = location
                    localLogManager.logSync(
                        "GeofenceManager: 地理围栏创建成功 - " +
                        "ID: $geofenceId, " +
                        "位置: (${location.latitude}, ${location.longitude}), " +
                        "半径: ${radius}m"
                    )
                }
                .addOnFailureListener { exception ->
                    localLogManager.logSync(
                        "GeofenceManager: 地理围栏创建失败 - ${exception.message}"
                    )
                }

        } catch (e: SecurityException) {
            localLogManager.logSync("GeofenceManager: 安全异常，无法创建地理围栏 - ${e.message}")
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceManager: 创建地理围栏时发生异常 - ${e.message}")
        }
    }

    /**
     * 移除当前的地理围栏
     */
    fun removeCurrentGeofence() {
        currentGeofenceId?.let { geofenceId ->
            try {
                geofencingClient.removeGeofences(listOf(geofenceId))
                    .addOnSuccessListener {
                        localLogManager.logSync("GeofenceManager: 地理围栏移除成功 - ID: $geofenceId")
                        currentGeofenceId = null
                        currentGeofenceLocation = null
                    }
                    .addOnFailureListener { exception ->
                        localLogManager.logSync("GeofenceManager: 地理围栏移除失败 - ${exception.message}")
                    }
            } catch (e: Exception) {
                localLogManager.logSync("GeofenceManager: 移除地理围栏时发生异常 - ${e.message}")
            }
        }
    }

    /**
     * 移除所有地理围栏
     */
    fun removeAllGeofences() {
        try {
            geofencingClient.removeGeofences(getGeofencePendingIntent())
                .addOnSuccessListener {
                    localLogManager.logSync("GeofenceManager: 所有地理围栏移除成功")
                    currentGeofenceId = null
                    currentGeofenceLocation = null
                }
                .addOnFailureListener { exception ->
                    localLogManager.logSync("GeofenceManager: 移除所有地理围栏失败 - ${exception.message}")
                }
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceManager: 移除所有地理围栏时发生异常 - ${e.message}")
        }
    }

    /**
     * 检查是否需要更新地理围栏
     * 当用户移动距离超过一定阈值时，创建新的地理围栏
     * @param newLocation 新的位置
     * @param threshold 距离阈值（米），默认为围栏半径的一半
     */
    fun checkAndUpdateGeofence(newLocation: Location, threshold: Float = GEOFENCE_RADIUS / 2) {
        currentGeofenceLocation?.let { currentLocation ->
            val distance = currentLocation.distanceTo(newLocation)
            if (distance > threshold) {
                localLogManager.logSync(
                    "GeofenceManager: 位置变化超过阈值(${threshold}m)，当前距离: ${distance}m，更新地理围栏"
                )
                createGeofence(newLocation)
            }
        } ?: run {
            // 如果没有当前围栏，直接创建新的
            localLogManager.logSync("GeofenceManager: 没有活跃的地理围栏，创建新围栏")
            createGeofence(newLocation)
        }
    }

    /**
     * 获取当前地理围栏信息
     */
    fun getCurrentGeofenceInfo(): Pair<String?, Location?> {
        return Pair(currentGeofenceId, currentGeofenceLocation)
    }

    /**
     * 检查地理围栏是否活跃
     */
    fun isGeofenceActive(): Boolean {
        return currentGeofenceId != null
    }
}
