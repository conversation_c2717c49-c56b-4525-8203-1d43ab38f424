package com.lfb.android.footprint.geofence

import android.content.Context
import android.content.SharedPreferences
import com.lfb.android.footprint.Manager.LocalLogManager

/**
 * 地理围栏配置管理
 * 管理地理围栏的各种配置选项
 */
object GeofenceConfig {
    
    private const val PREFS_NAME = "geofence_config"
    private const val KEY_USE_NATIVE_GEOFENCE = "use_native_geofence"
    private const val KEY_GEOFENCE_RADIUS = "geofence_radius"
    private const val KEY_UPDATE_THRESHOLD = "update_threshold"
    private const val KEY_CHECK_INTERVAL = "check_interval"
    private const val KEY_AUTO_DETECT_MODE = "auto_detect_mode"
    
    // 默认配置值
    private const val DEFAULT_GEOFENCE_RADIUS = 100f // 米
    private const val DEFAULT_UPDATE_THRESHOLD = 50f // 米
    private const val DEFAULT_CHECK_INTERVAL = 30000L // 毫秒
    private const val DEFAULT_AUTO_DETECT_MODE = true
    
    /**
     * 获取SharedPreferences
     */
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 是否使用原生地理围栏实现
     */
    fun useNativeGeofence(context: Context): Boolean {
        val prefs = getPrefs(context)
        return if (prefs.getBoolean(KEY_AUTO_DETECT_MODE, DEFAULT_AUTO_DETECT_MODE)) {
            // 自动检测模式
            !isGooglePlayServicesAvailable(context)
        } else {
            // 手动设置模式
            prefs.getBoolean(KEY_USE_NATIVE_GEOFENCE, true)
        }
    }
    
    /**
     * 设置地理围栏实现模式
     */
    fun setUseNativeGeofence(context: Context, useNative: Boolean) {
        getPrefs(context).edit()
            .putBoolean(KEY_USE_NATIVE_GEOFENCE, useNative)
            .putBoolean(KEY_AUTO_DETECT_MODE, false) // 关闭自动检测
            .apply()
            
        LocalLogManager.getInstance(context).logSync(
            "GeofenceConfig: 设置地理围栏模式 - ${if (useNative) "原生实现" else "Google Play Services"}"
        )
    }
    
    /**
     * 启用自动检测模式
     */
    fun enableAutoDetectMode(context: Context) {
        getPrefs(context).edit()
            .putBoolean(KEY_AUTO_DETECT_MODE, true)
            .apply()
            
        LocalLogManager.getInstance(context).logSync("GeofenceConfig: 启用自动检测模式")
    }
    
    /**
     * 获取地理围栏半径
     */
    fun getGeofenceRadius(context: Context): Float {
        return getPrefs(context).getFloat(KEY_GEOFENCE_RADIUS, DEFAULT_GEOFENCE_RADIUS)
    }
    
    /**
     * 设置地理围栏半径
     */
    fun setGeofenceRadius(context: Context, radius: Float) {
        getPrefs(context).edit()
            .putFloat(KEY_GEOFENCE_RADIUS, radius)
            .apply()
            
        LocalLogManager.getInstance(context).logSync("GeofenceConfig: 设置地理围栏半径 - ${radius}m")
    }
    
    /**
     * 获取更新阈值
     */
    fun getUpdateThreshold(context: Context): Float {
        return getPrefs(context).getFloat(KEY_UPDATE_THRESHOLD, DEFAULT_UPDATE_THRESHOLD)
    }
    
    /**
     * 设置更新阈值
     */
    fun setUpdateThreshold(context: Context, threshold: Float) {
        getPrefs(context).edit()
            .putFloat(KEY_UPDATE_THRESHOLD, threshold)
            .apply()
            
        LocalLogManager.getInstance(context).logSync("GeofenceConfig: 设置更新阈值 - ${threshold}m")
    }
    
    /**
     * 获取检查间隔（仅原生模式使用）
     */
    fun getCheckInterval(context: Context): Long {
        return getPrefs(context).getLong(KEY_CHECK_INTERVAL, DEFAULT_CHECK_INTERVAL)
    }
    
    /**
     * 设置检查间隔（仅原生模式使用）
     */
    fun setCheckInterval(context: Context, interval: Long) {
        getPrefs(context).edit()
            .putLong(KEY_CHECK_INTERVAL, interval)
            .apply()
            
        LocalLogManager.getInstance(context).logSync("GeofenceConfig: 设置检查间隔 - ${interval}ms")
    }
    
    /**
     * 检测Google Play Services是否可用
     */
    private fun isGooglePlayServicesAvailable(context: Context): Boolean {
        return try {
            // 尝试创建GeofencingClient来检测Google Play Services
            val testClient = com.google.android.gms.location.LocationServices.getGeofencingClient(context)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取当前配置信息
     */
    fun getConfigInfo(context: Context): String {
        val prefs = getPrefs(context)
        val autoDetect = prefs.getBoolean(KEY_AUTO_DETECT_MODE, DEFAULT_AUTO_DETECT_MODE)
        val useNative = useNativeGeofence(context)
        val gpsAvailable = isGooglePlayServicesAvailable(context)
        
        return buildString {
            append("=== 地理围栏配置信息 ===\n")
            append("自动检测模式: ${if (autoDetect) "启用" else "禁用"}\n")
            append("Google Play Services: ${if (gpsAvailable) "可用" else "不可用"}\n")
            append("当前使用模式: ${if (useNative) "原生实现" else "Google Play Services"}\n")
            append("地理围栏半径: ${getGeofenceRadius(context)}m\n")
            append("更新阈值: ${getUpdateThreshold(context)}m\n")
            append("检查间隔: ${getCheckInterval(context)}ms\n")
            
            if (!gpsAvailable && !autoDetect && !useNative) {
                append("\n⚠️ 警告: Google Play Services不可用，但配置为使用GPS模式")
            }
        }
    }
    
    /**
     * 重置为默认配置
     */
    fun resetToDefaults(context: Context) {
        getPrefs(context).edit().clear().apply()
        LocalLogManager.getInstance(context).logSync("GeofenceConfig: 重置为默认配置")
    }
    
    /**
     * 获取推荐配置
     */
    fun getRecommendedConfig(context: Context): String {
        val gpsAvailable = isGooglePlayServicesAvailable(context)
        
        return if (gpsAvailable) {
            "推荐使用Google Play Services模式，功能更完善，电池消耗更低"
        } else {
            "推荐使用原生模式，适用于没有Google Play Services的设备"
        }
    }
}
