package com.lfb.android.footprint.geofence

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.location.Location
import android.os.Build
import com.google.android.gms.location.Geofence
import com.google.android.gms.location.GeofencingEvent
import com.lfb.android.footprint.service.LocationService
import com.lfb.android.footprint.location.LocationManager

/**
 * 地理围栏广播接收器
 * 处理地理围栏进入和离开事件
 * 主要功能：
 * 1. 监听地理围栏触发事件
 * 2. 在离开围栏时启动定位服务（如果未运行）
 * 3. 在离开围栏时创建新的地理围栏
 */
class GeofenceBroadcastReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        val localLogManager = LocalLogManager.getInstance(context)
        
        try {
            localLogManager.logSync("GeofenceBroadcastReceiver: 收到地理围栏事件")
            
            val geofencingEvent = GeofencingEvent.fromIntent(intent)
            if (geofencingEvent == null) {
                localLogManager.logSync("GeofenceBroadcastReceiver: 无法解析地理围栏事件")
                return
            }

            if (geofencingEvent.hasError()) {
                localLogManager.logSync(
                    "GeofenceBroadcastReceiver: 地理围栏事件错误 - " +
                    "错误代码: ${geofencingEvent.errorCode}"
                )
                return
            }

            // 获取触发的地理围栏列表
            val triggeringGeofences = geofencingEvent.triggeringGeofences
            if (triggeringGeofences.isNullOrEmpty()) {
                localLogManager.logSync("GeofenceBroadcastReceiver: 没有触发的地理围栏")
                return
            }

            // 获取触发位置
            val triggeringLocation = geofencingEvent.triggeringLocation
            if (triggeringLocation == null) {
                localLogManager.logSync("GeofenceBroadcastReceiver: 无法获取触发位置")
                return
            }

            // 获取地理围栏转换类型
            val geofenceTransition = geofencingEvent.geofenceTransition

            // 记录详细的触发信息
            val geofenceIds = triggeringGeofences.map { it.requestId }.joinToString(", ")
            localLogManager.logSync(
                "GeofenceBroadcastReceiver: 地理围栏触发 - " +
                "类型: ${getTransitionString(geofenceTransition)}, " +
                "围栏ID: [$geofenceIds], " +
                "位置: (${triggeringLocation.latitude}, ${triggeringLocation.longitude}), " +
                "精度: ${triggeringLocation.accuracy}m"
            )

            // 处理不同的地理围栏转换类型
            when (geofenceTransition) {
                Geofence.GEOFENCE_TRANSITION_ENTER -> {
                    handleGeofenceEnter(context, triggeringGeofences, triggeringLocation)
                }
                Geofence.GEOFENCE_TRANSITION_EXIT -> {
                    handleGeofenceExit(context, triggeringGeofences, triggeringLocation)
                }
                Geofence.GEOFENCE_TRANSITION_DWELL -> {
                    handleGeofenceDwell(context, triggeringGeofences, triggeringLocation)
                }
                else -> {
                    localLogManager.logSync(
                        "GeofenceBroadcastReceiver: 未知的地理围栏转换类型: $geofenceTransition"
                    )
                }
            }

        } catch (e: Exception) {
            localLogManager.logSync(
                "GeofenceBroadcastReceiver: 处理地理围栏事件时发生异常 - ${e.message}"
            )
        }
    }

    /**
     * 处理进入地理围栏事件
     */
    private fun handleGeofenceEnter(
        context: Context,
        triggeringGeofences: List<Geofence>,
        location: Location
    ) {
        val localLogManager = LocalLogManager.getInstance(context)
        localLogManager.logSync("GeofenceBroadcastReceiver: 进入地理围栏")
        
        // 进入围栏时可以执行一些操作，比如记录进入时间等
        // 目前主要关注离开围栏的事件，所以这里暂时只记录日志
    }

    /**
     * 处理离开地理围栏事件
     * 这是最重要的事件，需要：
     * 1. 确保定位服务正在运行
     * 2. 在新位置创建新的地理围栏
     */
    private fun handleGeofenceExit(
        context: Context,
        triggeringGeofences: List<Geofence>,
        location: Location
    ) {
        val localLogManager = LocalLogManager.getInstance(context)
        localLogManager.logSync("GeofenceBroadcastReceiver: 离开地理围栏，开始后台唤醒流程")

        try {
            // 1. 启动定位服务（如果未运行）
            ensureLocationServiceRunning(context)

            // 2. 在新位置创建新的地理围栏
            createNewGeofenceAtLocation(context, location)

            localLogManager.logSync("GeofenceBroadcastReceiver: 后台唤醒流程完成")

        } catch (e: Exception) {
            localLogManager.logSync(
                "GeofenceBroadcastReceiver: 处理离开围栏事件时发生异常 - ${e.message}"
            )
        }
    }

    /**
     * 处理在地理围栏内停留事件
     */
    private fun handleGeofenceDwell(
        context: Context,
        triggeringGeofences: List<Geofence>,
        location: Location
    ) {
        val localLogManager = LocalLogManager.getInstance(context)
        localLogManager.logSync("GeofenceBroadcastReceiver: 在地理围栏内停留")
        
        // 停留事件可以用于优化，比如降低定位频率等
        // 目前暂时只记录日志
    }

    /**
     * 确保定位服务正在运行
     */
    private fun ensureLocationServiceRunning(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)

        try {
            localLogManager.logSync("GeofenceBroadcastReceiver: 检查并启动定位服务")

            val serviceManager = GeofenceServiceManager.getInstance(context)
            val success = serviceManager.ensureLocationServiceRunning()

            if (success) {
                localLogManager.logSync("GeofenceBroadcastReceiver: 定位服务确保运行成功")
            } else {
                localLogManager.logSync("GeofenceBroadcastReceiver: 定位服务确保运行失败")
            }

            // 记录当前状态
            val statusInfo = serviceManager.getServiceStatusInfo()
            localLogManager.logSync("GeofenceBroadcastReceiver: 当前状态 - $statusInfo")

        } catch (e: Exception) {
            localLogManager.logSync(
                "GeofenceBroadcastReceiver: 启动定位服务失败 - ${e.message}"
            )
        }
    }

    /**
     * 在指定位置创建新的地理围栏
     */
    private fun createNewGeofenceAtLocation(context: Context, location: Location) {
        val localLogManager = LocalLogManager.getInstance(context)
        
        try {
            localLogManager.logSync(
                "GeofenceBroadcastReceiver: 在新位置创建地理围栏 - " +
                "位置: (${location.latitude}, ${location.longitude})"
            )
            
            val geofenceManager = GeofenceManager.getInstance(context)
            geofenceManager.createGeofence(location)
            
        } catch (e: Exception) {
            localLogManager.logSync(
                "GeofenceBroadcastReceiver: 创建新地理围栏失败 - ${e.message}"
            )
        }
    }

    /**
     * 获取地理围栏转换类型的字符串描述
     */
    private fun getTransitionString(transitionType: Int): String {
        return when (transitionType) {
            Geofence.GEOFENCE_TRANSITION_ENTER -> "进入"
            Geofence.GEOFENCE_TRANSITION_EXIT -> "离开"
            Geofence.GEOFENCE_TRANSITION_DWELL -> "停留"
            else -> "未知($transitionType)"
        }
    }
}
