package com.lfb.android.footprint.ui.components.aboutScreen

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import com.lfb.android.footprint.GuideActivity
import com.lfb.android.footprint.prefs.AppPrefs
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.lfb.android.footprint.R
import com.lfb.android.footprint.ui.theme.mainRedColor
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig

@Composable
fun AboutScreen(
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val themeConfig = rememberMapThemeConfig()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(themeConfig.normalBackgroundColor)
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp),
            verticalArrangement = Arrangement.spacedBy(0.dp)
        ) {
            item {
                Spacer(modifier = Modifier.height(60.dp))
                
                // 应用图标和版本信息
                AppInfoSection()
                
                Spacer(modifier = Modifier.height(40.dp))
            }

            // 第一组功能
            item {
                AboutSectionGroup(
                    items = listOf(
                        "足迹已解锁成就",
                        "足迹贡献榜",
                        "作者小红书",
                        "作者微博"
                    ),
                    onItemClick = { index ->
                        handleFirstSectionClick(context, index)
                    }
                )
                
                Spacer(modifier = Modifier.height(20.dp))
            }

            // 第二组功能
            item {
                AboutSectionGroup(
                    items = listOf(
                        "引导页",
                        "我要再看一遍更新内容！",
                        "QQ群交流",
                    ),
                    onItemClick = { index ->
                        handleSecondSectionClick(context, index)
                    }
                )
                
                Spacer(modifier = Modifier.height(120.dp))
            }
        }

        // 返回按钮
        BackButton(
            onClick = onBackClick,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 50.dp)
        )
    }
}

@Composable
private fun AppInfoSection() {
    val themeConfig = rememberMapThemeConfig()
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
    ) {
        // 应用图标
        Image(
            painter = painterResource(id = R.drawable.app_logo),
            contentDescription = "App Logo",
            modifier = Modifier.size(76.dp)
                .shadow(
                    elevation = 2.dp,
                    shape = RoundedCornerShape(8.dp)
                )
                .clip(RoundedCornerShape(12.dp))
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 应用名称
        Text(
            text = "关于足迹",
            color = themeConfig.normalTextColor,
            fontSize = 24.sp,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 版本信息
        Text(
            text = "版本号：公测 v1.0[1]",
            color = themeConfig.normalTextColor.copy(alpha = 0.7f),
            fontSize = 14.sp
        )
    }
}

@Composable
private fun AboutSectionGroup(
    items: List<String>,
    onItemClick: (Int) -> Unit
) {
    val themeConfig = rememberMapThemeConfig()

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(themeConfig.normalBackgroundColor2)
    ) {
        items.forEachIndexed { index, item ->
            AboutItem(
                title = item,
                onClick = { onItemClick(index) },
                showDivider = index < items.size - 1
            )
        }
    }
}

@Composable
private fun AboutItem(
    title: String,
    onClick: () -> Unit,
    showDivider: Boolean = true
) {
    val themeConfig = rememberMapThemeConfig()

    Column {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onClick() }
                .padding(horizontal = 16.dp, vertical = 12.dp)
        ) {
            Text(
                text = title,
                color = Color.White,
                fontSize = 15.sp,
                modifier = Modifier.align(Alignment.CenterStart)
            )
        }
        
        if (showDivider) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(0.5.dp)
                    .padding(start = 16.dp)
                    .background(themeConfig.normalTextColor.copy(alpha = 0.2f))
            )
        }
    }
}

@Composable
private fun BackButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier
            .width(120.dp)
            .height(44.dp),
        shape = RoundedCornerShape(22.dp),
        border = BorderStroke(1.dp, mainRedColor),
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = Color.Transparent,
            contentColor = mainRedColor
        )
    ) {
        Text(
            text = "返回",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}


private fun handleFirstSectionClick(context: Context, index: Int) {
    when (index) {
        0 -> {
            // 足迹已解锁成就
            openUrl(context, "https://doc.steplife.cn/md/achievement.html")
        }
        1 -> {
            // 足迹贡献榜
            openUrl(context, "https://doc.steplife.cn/md/contribution.html")
        }
        2 -> {
            // 作者小红书
            openXiaohongshu(context)
        }
        3 -> {
            // 作者微博
            openWeibo(context)
        }
    }
}

private fun handleSecondSectionClick(context: Context, index: Int) {
    when (index) {
        0 -> {
            // 引导页 - 重新显示引导页面
            showGuideScreen(context)
        }
        1 -> {
            // 我要再看一遍更新内容 - TODO: 实现新功能展示
        }
        2 -> {
            // QQ群交流
            joinQQGroup(context)
        }
        3 -> {
            // 鼓励一下足迹 - 跳转到应用商店评价
            openAppStore(context)
        }
    }
}

private fun showGuideScreen(context: Context) {
    try {
        val intent = Intent(context, GuideActivity::class.java)
        intent.putExtra("force_show_guide", true)
        context.startActivity(intent)
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

private fun openUrl(context: Context, url: String) {
    try {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        context.startActivity(intent)
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

private fun openXiaohongshu(context: Context) {
    try {
        // 尝试打开小红书App
        val appIntent = Intent(Intent.ACTION_VIEW, Uri.parse("xhsdiscover://user/5c619d46000000001a02eedc"))
        if (isAppAvailable(context, appIntent)) {
            context.startActivity(appIntent)
        } else {
            // 如果App不可用，打开网页版
            openUrl(context, "https://www.xiaohongshu.com/user/profile/5c619d46000000001a02eedc")
        }
    } catch (e: Exception) {
        openUrl(context, "https://www.xiaohongshu.com/user/profile/5c619d46000000001a02eedc")
    }
}

private fun openWeibo(context: Context) {
    // 直接打开微博网页版
    openUrl(context, "https://m.weibo.cn/u/2009667563")
}

private fun joinQQGroup(context: Context) {
    try {
        // QQ群号和key（这些值应该从配置中获取）
        val qqGroupNum = "123456789" // 替换为实际的QQ群号
        val key = "08ba6eeeebc856ee71db2e8000034805496da31ce681354bd0a41098e061cf35"
        
        val qqIntent = Intent(Intent.ACTION_VIEW, Uri.parse(
            "mqqapi://card/show_pslcard?src_type=internal&version=1&uin=$qqGroupNum&key=$key&card_type=group&source=external"
        ))
        
        if (isAppAvailable(context, qqIntent)) {
            context.startActivity(qqIntent)
        } else {
            // 如果QQ不可用，复制群号到剪贴板
            copyToClipboard(context, qqGroupNum, "已复制QQ群号到粘贴板")
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

private fun openAppStore(context: Context) {
    try {
        // 跳转到Google Play商店评价页面
        val packageName = context.packageName
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName"))
        if (isAppAvailable(context, intent)) {
            context.startActivity(intent)
        } else {
            // 如果Play商店不可用，打开网页版
            openUrl(context, "https://play.google.com/store/apps/details?id=$packageName")
        }
    } catch (e: Exception) {
        openUrl(context, "https://play.google.com/store/apps/details?id=${context.packageName}")
    }
}

private fun isAppAvailable(context: Context, intent: Intent): Boolean {
    val packageManager = context.packageManager
    val activities = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
    return activities.isNotEmpty()
}

private fun copyToClipboard(context: Context, text: String, message: String) {
    try {
        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
        val clip = android.content.ClipData.newPlainText("QQ群号", text)
        clipboard.setPrimaryClip(clip)
        
        // TODO: 显示Toast提示
        android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_SHORT).show()
    } catch (e: Exception) {
        e.printStackTrace()
    }
}
