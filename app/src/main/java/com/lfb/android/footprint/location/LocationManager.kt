package com.lfb.android.footprint.location

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.os.Build
import android.os.Looper
import android.util.Log
import android.annotation.SuppressLint
import android.icu.text.SimpleDateFormat
import com.google.android.gms.location.*
import com.lfb.android.footprint.Manager.AppNotificationManager
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.location.GpsOffsetFilter
import com.lfb.android.footprint.geofence.GeofenceManager
import com.lfb.android.footprint.geofence.NativeGeofenceManager
import com.lfb.android.footprint.geofence.GeofenceConfig
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.Locale
import kotlin.math.abs
import kotlin.math.sqrt

class LocationManager private constructor(private val context: Context) {

    private val TAG = "LocationManager"

    private val _locationFlow = MutableSharedFlow<Location>()
    val locationFlow = _locationFlow.asSharedFlow()

    private val _gpsSignalFlow = MutableStateFlow(0)
    val gpsSignalFlow = _gpsSignalFlow.asStateFlow()

    private val systemLocationManager: android.location.LocationManager by lazy {
        context.getSystemService(Context.LOCATION_SERVICE) as android.location.LocationManager
    }
    private val locationDataRecorder by lazy { LocationDataRecorder.getInstance(this.context) }
    private val gpsOffsetFilter by lazy { GpsOffsetFilter(context) }
    private val geofenceManager by lazy { GeofenceManager.getInstance(context) }
    private val nativeGeofenceManager by lazy { NativeGeofenceManager.getInstance(context) }

    var lastLocation: Location? = null

    // 地理围栏相关状态
    private var hasCreatedInitialGeofence = false
    private var lastGeofenceUpdateLocation: Location? = null

    // 地理围栏模式选择：true=使用原生实现，false=使用Google Play Services
    private var useNativeGeofence = true

    init {
        lastLocation = locationDataRecorder.lastLocation

        // 从配置中获取地理围栏实现模式
        useNativeGeofence = GeofenceConfig.useNativeGeofence(context)
        LocalLogManager.getInstance(context).logSync(
            "LocationManager: 地理围栏模式 - ${if (useNativeGeofence) "原生实现" else "Google Play Services"}"
        )
    }

    private val locationListener = android.location.LocationListener { location ->
//        Log.e(TAG, "locationupdate: ${location}")

        var theLocationAllowed = false
        var locationDistance = 0f
        val previousLocation = this.lastLocation // 将 this.lastLocation 赋给一个局部变量，更清晰

        if (previousLocation != null) {
            // 判断是否是同一天
            val sameDay = SimpleDateFormat("yyyyMMdd", Locale.getDefault()).let { sdf ->
                sdf.format(location.time) == sdf.format(previousLocation.time)
            }
            if (sameDay == false) {
                // 新的一天
                theLocationAllowed = true
            } else {
                // 同一天
                val subTime = abs(location.time - previousLocation.time)
                if (subTime > 1000) {
                    var distanceAllow = 100.0;
                    if (location.speed > 0) {
                        distanceAllow = 20+sqrt(location.speed*5)*6.5;
                    }

                    val distance = location.distanceTo(previousLocation)
                    locationDistance = distance
                    if (distance > distanceAllow) {
                        theLocationAllowed = true
                    } else {
                        LocalLogManager.getInstance(this.context).logSync("distance(${distance}) distanceAllow(${distanceAllow}) subTime(${subTime}) speed(${location.speed}) lc: ${location}")
                    }
                }
            }
        } else {
            theLocationAllowed = true
        }

        // 应用GPS偏移过滤器
        if (theLocationAllowed) {
            // 首先记录原始定位数据（未经过滤的）
            locationDataRecorder.recordRawLocation(location)

            theLocationAllowed = gpsOffsetFilter.shouldAcceptLocation(location)
            if (!theLocationAllowed) {
                LocalLogManager.getInstance(this.context).logSync("GPS偏移过滤器拒绝: lat=${location.latitude}, lng=${location.longitude}, accuracy=${location.accuracy}")
            }
        }

        this.lastLocation = location

        // 地理围栏管理 - 基于原始GPS数据，不依赖过滤结果
        handleGeofenceManagement(location)

        // 如果使用原生地理围栏，同时更新位置到原生管理器
        if (useNativeGeofence) {
            nativeGeofenceManager.updateLocation(location)
        }

        if (theLocationAllowed) {
            CoroutineScope(Dispatchers.IO).launch {
                _locationFlow.emit(location)
                locationDataRecorder.recordLocation(location)
            }
        }

        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val title = "位置更新:${sdf.format(location.time)}"
        val text = String.format(
            "%s %.1f 当前位置: %.3f, %.3f",
            theLocationAllowed,
            locationDistance,
            location.latitude,
            location.longitude
        )

        AppNotificationManager.showLocationUpdateNotification(context, title, text)
    }

    @SuppressLint("MissingPermission")
    fun startLocationUpdates() {
        if (!hasRequiredPermissions()) return

        LocalLogManager.getInstance(context).logSync("LocationManager: 开始位置更新，重置地理围栏状态")

        // 重置地理围栏状态
        resetGeofenceState()

        var minTime = 1200L
        var minDistance = 100f

        if (AppPrefs.sharedInstance.runningModel == 1)  {
            // 耗电模式
            minTime = 1000L
            minDistance = 20f
        } else if (AppPrefs.sharedInstance.runningModel == 2)  {
            // 省电模式
            minTime = 2000L
            minDistance = 200f
        }

        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
            systemLocationManager.requestLocationUpdates(
                android.location.LocationManager.GPS_PROVIDER,
                minTime,
                minDistance,
                locationListener,
                Looper.getMainLooper()
            )
        }

        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
            systemLocationManager.requestLocationUpdates(
                android.location.LocationManager.NETWORK_PROVIDER,
                minTime,
                minDistance,
                locationListener,
                Looper.getMainLooper()
            )
        }
    }

    private fun hasRequiredPermissions(): Boolean {
        return context.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
               context.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
               (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q ||
                context.checkSelfPermission(Manifest.permission.ACCESS_BACKGROUND_LOCATION) == PackageManager.PERMISSION_GRANTED)
    }

    fun stopLocationUpdates() {
        LocalLogManager.getInstance(context).logSync("LocationManager: 停止位置更新")
        systemLocationManager.removeUpdates(locationListener)
        // 注意：不重置地理围栏状态，让地理围栏继续工作以便后台唤醒
    }

    /**
     * 处理地理围栏管理
     * 基于原始GPS数据，不依赖位置过滤结果
     */
    private fun handleGeofenceManagement(location: Location) {
        try {
            // 检查GPS精度，只有精度足够好的位置才用于地理围栏
            if (location.accuracy > 100) {
                LocalLogManager.getInstance(context).logSync(
                    "LocationManager: GPS精度不足(${location.accuracy}m)，跳过地理围栏更新"
                )
                return
            }

            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 处理地理围栏管理 - " +
                "位置: (${location.latitude}, ${location.longitude}), " +
                "精度: ${location.accuracy}m"
            )

            // 如果还没有创建初始地理围栏，立即创建
            if (!hasCreatedInitialGeofence) {
                createInitialGeofence(location)
                hasCreatedInitialGeofence = true
                lastGeofenceUpdateLocation = location
            } else {
                // 检查是否需要更新地理围栏
                checkAndUpdateGeofence(location)
            }

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 地理围栏管理异常 - ${e.message}"
            )
        }
    }

    /**
     * 创建初始地理围栏
     */
    private fun createInitialGeofence(location: Location) {
        try {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 创建初始地理围栏 - " +
                "位置: (${location.latitude}, ${location.longitude}), " +
                "精度: ${location.accuracy}m, " +
                "模式: ${if (useNativeGeofence) "原生" else "Google Play Services"}"
            )

            val radius = GeofenceConfig.getGeofenceRadius(context)
            if (useNativeGeofence) {
                nativeGeofenceManager.createGeofence(location, radius)
            } else {
                geofenceManager.createGeofence(location, radius)
            }

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 创建初始地理围栏失败 - ${e.message}"
            )
        }
    }

    /**
     * 检查并更新地理围栏
     */
    private fun checkAndUpdateGeofence(location: Location) {
        try {
            lastGeofenceUpdateLocation?.let { lastLocation ->
                val distance = lastLocation.distanceTo(location)
                val threshold = GeofenceConfig.getUpdateThreshold(context)

                if (distance > threshold) {
                    LocalLogManager.getInstance(context).logSync(
                        "LocationManager: 位置变化超过阈值(${threshold}m)，当前距离: ${distance}m，更新地理围栏"
                    )
                    val radius = GeofenceConfig.getGeofenceRadius(context)
                    if (useNativeGeofence) {
                        nativeGeofenceManager.createGeofence(location, radius)
                    } else {
                        geofenceManager.createGeofence(location, radius)
                    }
                    lastGeofenceUpdateLocation = location
                } else {
                    LocalLogManager.getInstance(context).logSync(
                        "LocationManager: 位置变化未超过阈值，距离: ${distance}m"
                    )
                }
            } ?: run {
                // 如果没有上次更新位置记录，直接更新
                LocalLogManager.getInstance(context).logSync(
                    "LocationManager: 没有上次地理围栏位置记录，创建新围栏"
                )
                val radius = GeofenceConfig.getGeofenceRadius(context)
                if (useNativeGeofence) {
                    nativeGeofenceManager.createGeofence(location, radius)
                } else {
                    geofenceManager.createGeofence(location, radius)
                }
                lastGeofenceUpdateLocation = location
            }

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 检查地理围栏更新失败 - ${e.message}"
            )
        }
    }

    /**
     * 重置地理围栏状态
     * 在停止定位或重新开始时调用
     */
    fun resetGeofenceState() {
        try {
            LocalLogManager.getInstance(context).logSync("LocationManager: 重置地理围栏状态")
            hasCreatedInitialGeofence = false
            lastGeofenceUpdateLocation = null
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 重置地理围栏状态失败 - ${e.message}"
            )
        }
    }

    /**
     * 检测Google Play Services是否可用
     */
    private fun isGooglePlayServicesAvailable(): Boolean {
        return try {
            // 尝试创建GeofencingClient来检测Google Play Services
            val testClient = com.google.android.gms.location.LocationServices.getGeofencingClient(context)
            true
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: Google Play Services不可用 - ${e.message}"
            )
            false
        }
    }

    /**
     * 获取地理围栏状态信息
     */
    fun getGeofenceStatus(): String {
        return try {
            buildString {
                append("=== LocationManager地理围栏状态 ===\n")
                append("地理围栏模式: ${if (useNativeGeofence) "原生实现" else "Google Play Services"}\n")
                append("初始围栏已创建: $hasCreatedInitialGeofence\n")
                append("配置半径: ${GeofenceConfig.getGeofenceRadius(context)}m\n")
                append("更新阈值: ${GeofenceConfig.getUpdateThreshold(context)}m\n")

                if (useNativeGeofence) {
                    append("\n=== 原生地理围栏详细状态 ===\n")
                    append(nativeGeofenceManager.getGeofenceStatus())
                } else {
                    append("\n=== Google Play Services地理围栏详细状态 ===\n")
                    val (geofenceId, geofenceLocation) = geofenceManager.getCurrentGeofenceInfo()
                    append("当前围栏ID: ${geofenceId ?: "无"}\n")
                    append("围栏位置: ${geofenceLocation?.let { "(${it.latitude}, ${it.longitude})" } ?: "无"}")
                }

                append("\n\n=== LocationManager状态 ===\n")
                append("上次更新位置: ${lastGeofenceUpdateLocation?.let { "(${it.latitude}, ${it.longitude})" } ?: "无"}")
            }
        } catch (e: Exception) {
            "获取地理围栏状态失败: ${e.message}"
        }
    }

    /**
     * 强制切换地理围栏模式
     * @param useNative true=使用原生实现，false=使用Google Play Services
     */
    fun switchGeofenceMode(useNative: Boolean) {
        try {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 切换地理围栏模式 - ${if (useNative) "原生实现" else "Google Play Services"}"
            )

            // 移除当前地理围栏
            if (useNativeGeofence) {
                nativeGeofenceManager.removeAllGeofences()
            } else {
                geofenceManager.removeAllGeofences()
            }

            // 切换模式
            useNativeGeofence = useNative

            // 重置状态
            resetGeofenceState()

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "LocationManager: 切换地理围栏模式失败 - ${e.message}"
            )
        }
    }

    companion object {
        @Volatile
        private var instance: LocationManager? = null

        fun getInstance(context: Context): LocationManager {
            return instance ?: synchronized(this) {
                instance ?: LocationManager(context.applicationContext).also { instance = it }
            }
        }
    }
}