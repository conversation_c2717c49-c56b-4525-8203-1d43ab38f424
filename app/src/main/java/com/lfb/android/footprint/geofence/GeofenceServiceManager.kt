package com.lfb.android.footprint.geofence

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import com.lfb.android.footprint.service.LocationService

/**
 * 地理围栏服务管理器
 * 负责检查和管理LocationService的运行状态
 * 确保在地理围栏触发时能够正确启动定位服务
 */
class GeofenceServiceManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: GeofenceServiceManager? = null

        fun getInstance(context: Context): GeofenceServiceManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: GeofenceServiceManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val localLogManager by lazy {
        LocalLogManager.getInstance(context)
    }

    /**
     * 检查LocationService是否正在运行
     */
    fun isLocationServiceRunning(): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            val isRunning = runningServices.any { serviceInfo ->
                serviceInfo.service.className == LocationService::class.java.name
            }
            
            localLogManager.logSync("GeofenceServiceManager: LocationService运行状态 - $isRunning")
            isRunning
            
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceServiceManager: 检查服务状态失败 - ${e.message}")
            false
        }
    }

    /**
     * 启动LocationService
     * 如果服务未运行，则启动它
     */
    fun startLocationService(): Boolean {
        return try {
            localLogManager.logSync("GeofenceServiceManager: 准备启动LocationService")
            
            val intent = Intent(context, LocationService::class.java).apply {
                action = "GEOFENCE_TRIGGERED_START"
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
                localLogManager.logSync("GeofenceServiceManager: 前台LocationService启动命令已发送")
            } else {
                context.startService(intent)
                localLogManager.logSync("GeofenceServiceManager: LocationService启动命令已发送")
            }
            
            true
            
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceServiceManager: 启动LocationService失败 - ${e.message}")
            false
        }
    }

    /**
     * 确保LocationService正在运行
     * 如果未运行则启动，如果已运行则记录日志
     */
    fun ensureLocationServiceRunning(): Boolean {
        return try {
            if (isLocationServiceRunning()) {
                localLogManager.logSync("GeofenceServiceManager: LocationService已在运行，无需启动")
                true
            } else {
                localLogManager.logSync("GeofenceServiceManager: LocationService未运行，开始启动")
                startLocationService()
            }
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceServiceManager: 确保服务运行时发生异常 - ${e.message}")
            false
        }
    }

    /**
     * 停止LocationService
     * 主要用于测试或特殊情况
     */
    fun stopLocationService(): Boolean {
        return try {
            localLogManager.logSync("GeofenceServiceManager: 准备停止LocationService")
            
            val intent = Intent(context, LocationService::class.java)
            val result = context.stopService(intent)
            
            localLogManager.logSync("GeofenceServiceManager: LocationService停止结果 - $result")
            result
            
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceServiceManager: 停止LocationService失败 - ${e.message}")
            false
        }
    }

    /**
     * 获取服务运行状态的详细信息
     */
    fun getServiceStatusInfo(): String {
        return try {
            val isRunning = isLocationServiceRunning()
            val geofenceManager = GeofenceManager.getInstance(context)
            val (geofenceId, geofenceLocation) = geofenceManager.getCurrentGeofenceInfo()
            
            buildString {
                append("LocationService运行状态: $isRunning\n")
                append("地理围栏状态: ${if (geofenceManager.isGeofenceActive()) "活跃" else "未激活"}\n")
                append("当前围栏ID: ${geofenceId ?: "无"}\n")
                append("围栏位置: ${geofenceLocation?.let { "(${it.latitude}, ${it.longitude})" } ?: "无"}")
            }
            
        } catch (e: Exception) {
            "获取状态信息失败: ${e.message}"
        }
    }

    /**
     * 重置地理围栏系统
     * 停止服务并清除所有地理围栏
     */
    fun resetGeofenceSystem(): Boolean {
        return try {
            localLogManager.logSync("GeofenceServiceManager: 开始重置地理围栏系统")
            
            // 移除所有地理围栏
            val geofenceManager = GeofenceManager.getInstance(context)
            geofenceManager.removeAllGeofences()
            
            // 停止服务
            stopLocationService()
            
            localLogManager.logSync("GeofenceServiceManager: 地理围栏系统重置完成")
            true
            
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceServiceManager: 重置地理围栏系统失败 - ${e.message}")
            false
        }
    }

    /**
     * 初始化地理围栏系统
     * 启动服务并等待创建初始地理围栏
     */
    fun initializeGeofenceSystem(): Boolean {
        return try {
            localLogManager.logSync("GeofenceServiceManager: 开始初始化地理围栏系统")
            
            // 确保LocationService运行
            val serviceStarted = ensureLocationServiceRunning()
            
            if (serviceStarted) {
                localLogManager.logSync("GeofenceServiceManager: 地理围栏系统初始化完成")
            } else {
                localLogManager.logSync("GeofenceServiceManager: 地理围栏系统初始化失败 - 服务启动失败")
            }
            
            serviceStarted
            
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceServiceManager: 初始化地理围栏系统失败 - ${e.message}")
            false
        }
    }
}
