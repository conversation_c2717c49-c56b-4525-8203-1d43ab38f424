package com.lfb.android.footprint.geofence

import android.content.Context
import com.lfb.android.footprint.Manager.LocalLogManager
import com.lfb.android.footprint.location.LocationManager

/**
 * 原生地理围栏使用示例
 * 专门针对没有Google Play Services的设备（如小米手机）
 */
object NativeGeofenceUsageExample {

    /**
     * 检查设备兼容性并初始化
     * 适用于小米、华为等国内厂商设备
     */
    fun initializeForChineseDevices(context: Context): Boolean {
        val localLogManager = LocalLogManager.getInstance(context)
        
        try {
            localLogManager.logSync("NativeGeofenceUsageExample: 检查设备兼容性")
            
            // 检查Google Play Services可用性
            val gpsAvailable = isGooglePlayServicesAvailable(context)
            localLogManager.logSync("NativeGeofenceUsageExample: Google Play Services可用性 - $gpsAvailable")
            
            if (!gpsAvailable) {
                localLogManager.logSync("NativeGeofenceUsageExample: 检测到无Google Play Services设备，使用原生模式")
                
                // 强制使用原生地理围栏
                GeofenceConfig.setUseNativeGeofence(context, true)
                
                // 设置适合的配置
                setupOptimalConfigForNativeMode(context)
                
                // 初始化系统
                val success = GeofenceInitializer.initialize(context)
                
                if (success) {
                    localLogManager.logSync("NativeGeofenceUsageExample: 原生地理围栏系统初始化成功")
                    
                    // 显示配置信息
                    val configInfo = GeofenceConfig.getConfigInfo(context)
                    localLogManager.logSync("NativeGeofenceUsageExample: 当前配置 - $configInfo")
                    
                } else {
                    localLogManager.logSync("NativeGeofenceUsageExample: 原生地理围栏系统初始化失败")
                }
                
                return success
                
            } else {
                localLogManager.logSync("NativeGeofenceUsageExample: 检测到Google Play Services可用，使用标准模式")
                
                // 启用自动检测模式
                GeofenceConfig.enableAutoDetectMode(context)
                
                // 使用标准初始化
                return GeofenceInitializer.initialize(context)
            }
            
        } catch (e: Exception) {
            localLogManager.logSync("NativeGeofenceUsageExample: 初始化过程中发生异常 - ${e.message}")
            return false
        }
    }

    /**
     * 为原生模式设置最优配置
     */
    private fun setupOptimalConfigForNativeMode(context: Context) {
        val localLogManager = LocalLogManager.getInstance(context)
        
        try {
            localLogManager.logSync("NativeGeofenceUsageExample: 设置原生模式最优配置")
            
            // 设置地理围栏半径（稍大一些以减少误触发）
            GeofenceConfig.setGeofenceRadius(context, 120f)
            
            // 设置更新阈值（稍小一些以更及时响应）
            GeofenceConfig.setUpdateThreshold(context, 60f)
            
            // 设置检查间隔（适中的频率平衡电池消耗和响应速度）
            GeofenceConfig.setCheckInterval(context, 45000L) // 45秒
            
            localLogManager.logSync("NativeGeofenceUsageExample: 原生模式配置设置完成")
            
        } catch (e: Exception) {
            localLogManager.logSync("NativeGeofenceUsageExample: 设置配置时发生异常 - ${e.message}")
        }
    }

    /**
     * 检测Google Play Services可用性
     */
    private fun isGooglePlayServicesAvailable(context: Context): Boolean {
        return try {
            val testClient = com.google.android.gms.location.LocationServices.getGeofencingClient(context)
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取设备兼容性报告
     */
    fun getDeviceCompatibilityReport(context: Context): String {
        return try {
            val gpsAvailable = isGooglePlayServicesAvailable(context)
            val currentMode = GeofenceConfig.useNativeGeofence(context)
            val deviceInfo = getDeviceInfo()
            
            buildString {
                append("=== 设备兼容性报告 ===\n")
                append("设备信息: $deviceInfo\n")
                append("Google Play Services: ${if (gpsAvailable) "可用" else "不可用"}\n")
                append("当前地理围栏模式: ${if (currentMode) "原生实现" else "Google Play Services"}\n")
                append("推荐模式: ${if (gpsAvailable) "Google Play Services" else "原生实现"}\n\n")
                
                if (!gpsAvailable) {
                    append("✅ 此设备适合使用原生地理围栏实现\n")
                    append("📱 常见于小米、华为、OPPO、vivo等国内品牌设备\n")
                    append("🔋 原生实现功耗稍高，但兼容性更好\n")
                } else {
                    append("✅ 此设备支持Google Play Services地理围栏\n")
                    append("🌍 推荐使用Google Play Services实现\n")
                    append("🔋 Google实现功耗更低，功能更完善\n")
                }
                
                append("\n=== 当前配置 ===\n")
                append(GeofenceConfig.getConfigInfo(context))
            }
            
        } catch (e: Exception) {
            "生成兼容性报告失败: ${e.message}"
        }
    }

    /**
     * 获取设备信息
     */
    private fun getDeviceInfo(): String {
        return try {
            "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL} (Android ${android.os.Build.VERSION.RELEASE})"
        } catch (e: Exception) {
            "未知设备"
        }
    }

    /**
     * 测试原生地理围栏功能
     */
    fun testNativeGeofence(context: Context): String {
        return try {
            val localLogManager = LocalLogManager.getInstance(context)
            val locationManager = LocationManager.getInstance(context)
            
            localLogManager.logSync("NativeGeofenceUsageExample: 开始测试原生地理围栏")
            
            // 强制切换到原生模式
            locationManager.switchGeofenceMode(true)
            
            // 获取状态
            val status = locationManager.getGeofenceStatus()
            
            buildString {
                append("=== 原生地理围栏测试结果 ===\n")
                append("测试时间: ${java.util.Date()}\n")
                append("设备: ${getDeviceInfo()}\n\n")
                append(status)
                append("\n\n✅ 测试完成，原生地理围栏功能正常")
            }
            
        } catch (e: Exception) {
            "测试失败: ${e.message}"
        }
    }

    /**
     * 为小米设备优化配置
     */
    fun optimizeForXiaomiDevices(context: Context) {
        try {
            val localLogManager = LocalLogManager.getInstance(context)
            localLogManager.logSync("NativeGeofenceUsageExample: 为小米设备优化配置")
            
            // 小米设备通常后台限制较严，需要更保守的配置
            GeofenceConfig.setGeofenceRadius(context, 150f) // 更大的半径
            GeofenceConfig.setUpdateThreshold(context, 75f) // 更大的更新阈值
            GeofenceConfig.setCheckInterval(context, 60000L) // 更长的检查间隔
            
            localLogManager.logSync("NativeGeofenceUsageExample: 小米设备优化配置完成")
            
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "NativeGeofenceUsageExample: 小米设备优化失败 - ${e.message}"
            )
        }
    }

    /**
     * 获取使用建议
     */
    fun getUsageRecommendations(context: Context): String {
        val gpsAvailable = isGooglePlayServicesAvailable(context)
        val manufacturer = android.os.Build.MANUFACTURER.lowercase()
        
        return buildString {
            append("=== 使用建议 ===\n\n")
            
            if (!gpsAvailable) {
                append("🔧 您的设备没有Google Play Services，建议：\n")
                append("1. 使用原生地理围栏实现\n")
                append("2. 在设置中将应用加入电池优化白名单\n")
                append("3. 允许应用自启动和后台运行\n")
                append("4. 关闭应用的省电模式限制\n\n")
                
                when {
                    manufacturer.contains("xiaomi") -> {
                        append("📱 小米设备特别建议：\n")
                        append("- 在MIUI安全中心中允许自启动\n")
                        append("- 设置无限制的后台活动\n")
                        append("- 关闭MIUI优化（开发者选项）\n")
                    }
                    manufacturer.contains("huawei") -> {
                        append("📱 华为设备特别建议：\n")
                        append("- 在手机管家中设置应用保护\n")
                        append("- 允许应用在锁屏后继续运行\n")
                        append("- 关闭智能省电模式\n")
                    }
                    manufacturer.contains("oppo") || manufacturer.contains("oneplus") -> {
                        append("📱 OPPO/OnePlus设备特别建议：\n")
                        append("- 在设置中允许应用自启动\n")
                        append("- 关闭应用冻结功能\n")
                        append("- 设置高耗电应用保护\n")
                    }
                }
            } else {
                append("✅ 您的设备支持Google Play Services\n")
                append("建议使用自动检测模式，系统会选择最优实现\n")
            }
        }
    }
}
