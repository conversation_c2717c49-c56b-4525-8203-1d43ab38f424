package com.lfb.android.footprint.geofence

import android.content.Context
import com.lfb.android.footprint.Manager.LocalLogManager
import com.lfb.android.footprint.location.LocationManager

/**
 * 地理围栏初始化工具类
 * 提供简单的API来初始化和管理地理围栏系统
 */
object GeofenceInitializer {

    /**
     * 初始化地理围栏系统
     * 建议在应用启动时调用
     * @param context 应用上下文
     * @return 初始化是否成功
     */
    fun initialize(context: Context): Boolean {
        val localLogManager = LocalLogManager.getInstance(context)
        
        return try {
            localLogManager.logSync("GeofenceInitializer: 开始初始化地理围栏系统")
            
            val serviceManager = GeofenceServiceManager.getInstance(context)
            val success = serviceManager.initializeGeofenceSystem()
            
            if (success) {
                localLogManager.logSync("GeofenceInitializer: 地理围栏系统初始化成功")
            } else {
                localLogManager.logSync("GeofenceInitializer: 地理围栏系统初始化失败")
            }
            
            success
            
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceInitializer: 初始化过程中发生异常 - ${e.message}")
            false
        }
    }

    /**
     * 重置地理围栏系统
     * 清除所有地理围栏并停止相关服务
     * @param context 应用上下文
     * @return 重置是否成功
     */
    fun reset(context: Context): Boolean {
        val localLogManager = LocalLogManager.getInstance(context)
        
        return try {
            localLogManager.logSync("GeofenceInitializer: 开始重置地理围栏系统")
            
            val serviceManager = GeofenceServiceManager.getInstance(context)
            val success = serviceManager.resetGeofenceSystem()
            
            if (success) {
                localLogManager.logSync("GeofenceInitializer: 地理围栏系统重置成功")
            } else {
                localLogManager.logSync("GeofenceInitializer: 地理围栏系统重置失败")
            }
            
            success
            
        } catch (e: Exception) {
            localLogManager.logSync("GeofenceInitializer: 重置过程中发生异常 - ${e.message}")
            false
        }
    }

    /**
     * 获取地理围栏系统状态
     * @param context 应用上下文
     * @return 状态信息字符串
     */
    fun getStatus(context: Context): String {
        return try {
            val serviceManager = GeofenceServiceManager.getInstance(context)
            val locationManager = LocationManager.getInstance(context)

            buildString {
                append("=== 地理围栏配置信息 ===\n")
                append(GeofenceConfig.getConfigInfo(context))
                append("\n\n=== 地理围栏系统状态 ===\n")
                append(serviceManager.getServiceStatusInfo())
                append("\n\n")
                append(locationManager.getGeofenceStatus())
            }
        } catch (e: Exception) {
            "获取状态失败: ${e.message}"
        }
    }

    /**
     * 检查地理围栏系统是否正常运行
     * @param context 应用上下文
     * @return 是否正常运行
     */
    fun isRunning(context: Context): Boolean {
        return try {
            val serviceManager = GeofenceServiceManager.getInstance(context)
            val geofenceManager = GeofenceManager.getInstance(context)
            
            serviceManager.isLocationServiceRunning() && geofenceManager.isGeofenceActive()
            
        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync(
                "GeofenceInitializer: 检查运行状态时发生异常 - ${e.message}"
            )
            false
        }
    }
}
